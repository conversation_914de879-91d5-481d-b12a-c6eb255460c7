import { json } from '@sveltejs/kit';
import type { <PERSON>quest<PERSON><PERSON><PERSON> } from './$types';
import { prisma } from '$lib/server/prisma';
import { verifySessionToken } from '$lib/server/auth';

export const POST: RequestHandler = async ({ request, cookies }) => {
  try {
    // Verify authentication
    const token = cookies.get('auth_token');
    if (!token) {
      return json({ error: 'Unauthorized' }, { status: 401 });
    }

    const user = await verifySessionToken(token);
    if (!user) {
      return json({ error: 'Invalid token' }, { status: 401 });
    }

    // Parse request body
    const { jobId, jobUrl, company, position, location } = await request.json();

    if (!company || !position) {
      return json({ error: 'Company and position are required' }, { status: 400 });
    }

    // Check if application already exists for this job URL
    if (jobUrl) {
      const existingApplication = await prisma.application.findFirst({
        where: {
          userId: user.id,
          url: jobUrl,
        },
      });

      if (existingApplication) {
        return json({ 
          message: 'Already applied to this job',
          application: existingApplication
        });
      }
    }

    // Create new application
    const application = await prisma.application.create({
      data: {
        userId: user.id,
        company,
        position,
        location: location || null,
        appliedDate: new Date(),
        status: 'Applied',
        url: jobUrl || null,
        jobType: 'Full-time',
        resumeUploaded: false,
      },
    });

    // If jobId is provided, update the job match result
    if (jobId) {
      try {
        await prisma.job_match_result.updateMany({
          where: {
            userId: user.id,
            jobId: jobId,
          },
          data: {
            applied: true,
          },
        });
      } catch (error) {
        console.error('Error updating job match result:', error);
        // Continue even if this fails
      }
    }

    return json({
      success: true,
      message: 'Application added successfully',
      application,
    });
  } catch (error) {
    console.error('Error adding application:', error);
    return json(
      { error: 'Failed to add application' },
      { status: 500 }
    );
  }
};
