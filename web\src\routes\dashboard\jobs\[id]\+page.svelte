<script lang="ts">
  import SEO from '$components/shared/SEO.svelte';
  import { Badge } from '$lib/components/ui/badge';
  import { Button } from '$lib/components/ui/button';
  import * as Card from '$lib/components/ui/card';
  import * as Tabs from '$lib/components/ui/tabs';
  import * as Dialog from '$lib/components/ui/dialog';
  import * as AlertDialog from '$lib/components/ui/alert-dialog';
  import { Label } from '$lib/components/ui/label';
  import { Textarea } from '$lib/components/ui/textarea';
  import {
    Briefcase,
    MapPin,
    Calendar,
    ExternalLink,
    Share2,
    Bookmark,
    BookmarkCheck,
    DollarSign,
    ChevronLeft,
    Building,
    CheckCircle,
    XCircle,
    Sparkles,
    Flag,
    ClipboardCheck,
  } from 'lucide-svelte';
  import { toast } from 'svelte-sonner';
  import { goto } from '$app/navigation';

  export let data;

  const { job, matchScore, similarJobs } = data;
  const title = job?.title || 'Job Details';
  let isJobSaved = data.isSaved || false;
  let isJobApplied = data.isApplied || false;
  const skillMatchData = data.skillMatchData || null;

  // Report dialog state
  let showReportDialog = false;
  let reportReason = '';

  // Apply confirmation dialog state
  let showApplyDialog = false;

  // Format match score as percentage
  function formatMatchScore(score: number | null) {
    if (score === null) return 'N/A';
    return `${Math.round(score * 100)}%`;
  }

  // Get color class based on match score
  function getScoreColorClass(score: number | null) {
    if (score === null) return 'bg-gray-100 text-gray-800';
    if (score >= 0.8) return 'bg-green-100 text-green-800';
    if (score >= 0.6) return 'bg-blue-100 text-blue-800';
    if (score >= 0.4) return 'bg-yellow-100 text-yellow-800';
    return 'bg-gray-100 text-gray-800';
  }

  // Get progress color based on score
  function getProgressColorClass(score: number) {
    if (score >= 0.8) return 'bg-green-500';
    if (score >= 0.6) return 'bg-blue-500';
    if (score >= 0.4) return 'bg-yellow-500';
    return 'bg-gray-500';
  }

  // Handle job application
  function applyToJob() {
    if (isJobApplied) {
      // If already applied, navigate to tracker
      goto('/dashboard/tracker');
      return;
    }

    // Open the job URL in a new tab first
    if (job.url) {
      window.open(job.url, '_blank', 'noopener,noreferrer');
    }

    // Show confirmation dialog
    showApplyDialog = true;
  }

  // Handle confirmation that user applied to the job
  async function confirmJobApplication() {
    try {
      // Show loading state
      const loadingToast = toast.loading('Adding to your tracker...');

      // Call the API to add the application to tracker
      const response = await fetch('/api/applications/add', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          jobId: job.id,
          jobUrl: job.url,
          company: job.company,
          position: job.title,
          location: job.location,
        }),
      });

      const result = await response.json();

      // Dismiss loading toast
      toast.dismiss(loadingToast);

      if (!response.ok) {
        throw new Error(result.error || 'Failed to add application');
      }

      // Update the applied state locally
      isJobApplied = true;

      // If the job was saved, update the saved state
      if (isJobSaved) {
        isJobSaved = false;
      }

      // Close the dialog
      showApplyDialog = false;

      toast.success('Application tracked', {
        description: 'Added to your application tracker',
      });

      // After a short delay, offer to navigate to the tracker
      setTimeout(() => {
        toast.message('View your application', {
          description: 'Go to your application tracker to manage it',
          action: {
            label: 'Go to Tracker',
            onClick: () => goto('/dashboard/tracker'),
          },
        });
      }, 2000);
    } catch (error) {
      console.error('Error adding application:', error);
      toast.error('Failed to add application', {
        description: error instanceof Error ? error.message : 'Please try again later',
      });
    }
  }

  // Handle job report
  async function reportJob() {
    // Validate the report reason
    if (!reportReason.trim()) {
      toast.error('Please provide a reason for reporting this job', {
        description: 'We need this information to review the listing',
      });
      return;
    }

    try {
      // Show loading state
      const loadingToast = toast.loading('Submitting report...');

      // Call the API to report the job
      const response = await fetch(`/api/jobs/${job.id}/report`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ reason: reportReason }),
      });

      // Dismiss loading toast regardless of result
      toast.dismiss(loadingToast);

      // Try to parse the JSON response, but handle cases where it might fail
      let result: { error?: string; success?: boolean; message?: string } = {};
      try {
        result = await response.json();
      } catch (e) {
        console.error('Error parsing JSON response:', e);
        result = { error: 'Invalid server response' };
      }

      if (!response.ok) {
        throw new Error(result.error || 'Failed to report job');
      }

      // Close dialog and reset form
      showReportDialog = false;
      reportReason = '';

      toast.success('Job reported', {
        description: 'Thank you for helping us maintain quality listings',
      });
    } catch (error) {
      console.error('Error reporting job:', error);

      // Make sure the dialog stays open if there's an error
      showReportDialog = true;

      toast.error('Failed to report job', {
        description: error instanceof Error ? error.message : 'Please try again later',
      });
    }
  }

  // Handle job save/bookmark
  async function saveJob() {
    // If job is already applied, don't allow saving/unsaving
    if (isJobApplied) {
      toast.info('This job is in your applications', {
        description: 'Applied jobs are automatically tracked',
      });
      return;
    }

    try {
      // Call the API to save/unsave the job
      const response = await fetch(`/api/jobs/${job.id}/save`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ notes: '' }),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Failed to save job');
      }

      // Toggle the saved state locally
      isJobSaved = !isJobSaved;

      toast.success(isJobSaved ? 'Job saved' : 'Job removed', {
        description: isJobSaved ? 'Added to your saved jobs' : 'Removed from your saved jobs',
      });
    } catch (error) {
      console.error('Error saving job:', error);
      toast.error('Failed to save job');
    }
  }

  // Handle share job
  function shareJob() {
    // Create a temporary input element to copy the URL
    const tempInput = document.createElement('input');
    tempInput.value = window.location.href;
    document.body.appendChild(tempInput);

    try {
      // First try the Web Share API (mobile devices)
      if (typeof navigator !== 'undefined' && navigator.share) {
        navigator
          .share({
            title: job?.title || 'Job Listing',
            text: `Check out this job: ${job?.title || 'Job Listing'} at ${job?.company || 'Company'}`,
            url: window.location.href,
          })
          .then(() => {
            toast.success('Job shared successfully');
          })
          .catch((error) => {
            console.error('Error sharing job:', error);
            // Fallback to clipboard
            fallbackCopyToClipboard(tempInput);
          });
      } else {
        // Fallback for browsers that don't support the Web Share API
        fallbackCopyToClipboard(tempInput);
      }
    } catch (error) {
      console.error('Error in share function:', error);
      fallbackCopyToClipboard(tempInput);
    } finally {
      // Clean up the temporary input
      document.body.removeChild(tempInput);
    }
  }

  // Fallback copy method that works in more browsers
  function fallbackCopyToClipboard(input: HTMLInputElement) {
    try {
      // Try the Clipboard API first
      if (navigator.clipboard && navigator.clipboard.writeText) {
        navigator.clipboard
          .writeText(window.location.href)
          .then(() => {
            toast.success('Link copied to clipboard', {
              description: 'You can now paste and share it',
            });
          })
          .catch((err) => {
            console.error('Clipboard API failed:', err);
            // Fall back to selection method
            selectAndCopy(input);
          });
      } else {
        // Fall back to selection method
        selectAndCopy(input);
      }
    } catch (error) {
      console.error('Failed to copy to clipboard:', error);
      // Last resort fallback
      selectAndCopy(input);
    }
  }

  // Helper function to select text and copy using document.execCommand
  function selectAndCopy(input: HTMLInputElement) {
    try {
      input.select();
      input.setSelectionRange(0, 99999); // For mobile devices

      // Using execCommand with a warning comment about deprecation
      // This is a fallback method for browsers that don't support clipboard API
      let successful = false;
      try {
        // @ts-ignore - execCommand is deprecated but still works in many browsers
        successful = document.execCommand('copy');
      } catch (e) {
        console.error('execCommand error:', e);
      }

      if (successful) {
        toast.success('Link copied to clipboard', {
          description: 'You can now paste and share it',
        });
      } else {
        throw new Error('Copy command failed');
      }
    } catch (err) {
      console.error('Selection copy failed:', err);
      toast.error('Could not copy link', {
        description: 'Please copy the URL from the address bar',
      });
    }
  }

  // Get time since posted
  function getTimeSincePosted(dateString: string | Date | null) {
    if (!dateString) return 'Recently';

    const postedDate = new Date(dateString);
    const now = new Date();
    const diffTime = Math.abs(now.getTime() - postedDate.getTime());
    const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays === 0) return 'Today';
    if (diffDays === 1) return 'Yesterday';
    if (diffDays < 7) return `${diffDays} days ago`;
    if (diffDays < 30) return `${Math.floor(diffDays / 7)} weeks ago`;
    return `${Math.floor(diffDays / 30)} months ago`;
  }

  // Go back to previous page
  function goBack() {
    window.history.back();
  }
</script>

<SEO
  title="{title} | Hirli"
  description="View detailed information about this job opportunity and take action. Apply, save, or share this job with others."
  keywords="job details, job opportunity, job application, career search, job description, {job?.title}, {job?.company}" />

<div class="container mx-auto px-4 py-8">
  <!-- Back button -->
  <Button variant="outline" class="mb-6" onclick={goBack}>
    <ChevronLeft class="mr-2 h-4 w-4" />
    Back to Matches
  </Button>

  <div class="grid grid-cols-1 gap-6 lg:grid-cols-3">
    <!-- Job details (left side) -->
    <div class="lg:col-span-2">
      <Card.Root>
        <!-- Action buttons at the top -->
        <div class="flex flex-wrap items-center justify-between gap-2 border-b p-4">
          <div class="flex flex-wrap gap-2">
            <Button
              variant={isJobSaved ? 'default' : 'outline'}
              size="sm"
              onclick={saveJob}
              disabled={isJobApplied}>
              {#if isJobSaved}
                <BookmarkCheck class="mr-2 h-4 w-4" />
                Saved
              {:else}
                <Bookmark class="mr-2 h-4 w-4" />
                Save
              {/if}
            </Button>
            <Button variant="outline" size="sm" onclick={shareJob}>
              <Share2 class="mr-2 h-4 w-4" />
              Share
            </Button>
            <Button variant="outline" size="sm" onclick={() => (showReportDialog = true)}>
              <Flag class="mr-2 h-4 w-4" />
              Report
            </Button>
            {#if job.url}
              <Button variant="outline" size="sm" asChild>
                <a href={job.url} target="_blank" rel="noopener noreferrer">
                  <ExternalLink class="mr-2 h-4 w-4" />
                  View Original
                </a>
              </Button>
            {/if}
          </div>
          <Button
            size="sm"
            onclick={applyToJob}
            class={isJobApplied
              ? 'bg-green-600 hover:bg-green-700'
              : 'bg-primary hover:bg-primary/90'}>
            {#if isJobApplied}
              <ClipboardCheck class="mr-2 h-4 w-4" />
              View Application
            {:else}
              <Sparkles class="mr-2 h-4 w-4" />
              Apply Now
            {/if}
          </Button>
        </div>

        <Card.Header class="p-6 pb-3">
          <div class="flex items-start justify-between">
            <div class="flex-1">
              {#if matchScore !== null}
                <Badge variant="secondary" class={`${getScoreColorClass(matchScore)} mb-2`}>
                  <Sparkles class="mr-1 h-3 w-3" />
                  {formatMatchScore(matchScore)} Match
                </Badge>
              {/if}
              <Card.Title class="text-2xl font-bold">{job.title}</Card.Title>
              <Card.Description class="text-primary text-lg font-medium">
                {job.company}
              </Card.Description>
              <div class="text-muted-foreground mt-2 flex items-center gap-2 text-sm">
                <MapPin class="h-4 w-4" />
                <span>{job.location || 'Remote'}</span>
              </div>
            </div>
            <div class="ml-4 flex-shrink-0">
              <div
                class="bg-primary/10 text-primary flex h-16 w-16 items-center justify-center rounded-md">
                <Building class="h-8 w-8" />
              </div>
            </div>
          </div>
        </Card.Header>

        <Card.Content class="p-6 pt-3">
          <!-- Key job details section -->
          <div class="bg-muted/5 mb-6 rounded-lg border p-4">
            <h3 class="text-muted-foreground mb-3 text-sm font-medium">Key Job Details</h3>
            <div class="grid grid-cols-2 gap-x-6 gap-y-4 md:grid-cols-3">
              {#if job.salary}
                <div class="flex flex-col items-start">
                  <div class="text-primary flex items-center gap-1.5 text-sm font-medium">
                    <DollarSign class="h-4 w-4" />
                    <span>Salary</span>
                  </div>
                  <p class="mt-1 text-sm font-medium">{job.salary}</p>
                </div>
              {/if}
              <div class="flex flex-col items-start">
                <div class="text-primary flex items-center gap-1.5 text-sm font-medium">
                  <Briefcase class="h-4 w-4" />
                  <span>Job Type</span>
                </div>
                <div class="mt-1 flex items-center gap-2">
                  <p class="text-sm font-medium">{job.employmentType || 'Full-time'}</p>
                  {#if job.remoteType === 'remote'}
                    <Badge variant="outline" class="text-xs">Remote</Badge>
                  {/if}
                </div>
              </div>
              <div class="flex flex-col items-start">
                <div class="text-primary flex items-center gap-1.5 text-sm font-medium">
                  <MapPin class="h-4 w-4" />
                  <span>Location</span>
                </div>
                <p class="mt-1 text-sm font-medium">{job.location || 'Remote'}</p>
              </div>
              <div class="flex flex-col items-start">
                <div class="text-primary flex items-center gap-1.5 text-sm font-medium">
                  <Calendar class="h-4 w-4" />
                  <span>Posted</span>
                </div>
                <p class="mt-1 text-sm font-medium">
                  {job.postedDate ? getTimeSincePosted(job.postedDate) : 'Recently'}
                </p>
              </div>
              {#if job.experienceLevel}
                <div class="flex flex-col items-start">
                  <div class="text-primary flex items-center gap-1.5 text-sm font-medium">
                    <Briefcase class="h-4 w-4" />
                    <span>Experience</span>
                  </div>
                  <p class="mt-1 text-sm font-medium">{job.experienceLevel}</p>
                </div>
              {/if}
              {#if job.company}
                <div class="flex flex-col items-start">
                  <div class="text-primary flex items-center gap-1.5 text-sm font-medium">
                    <Building class="h-4 w-4" />
                    <span>Company</span>
                  </div>
                  <p class="mt-1 text-sm font-medium">{job.company}</p>
                </div>
              {/if}
            </div>
          </div>

          <!-- Apply with Hirli section -->
          <div class="mb-6 overflow-hidden rounded-lg border">
            <div class="bg-primary/10 p-4">
              <div class="flex items-center justify-between">
                <div class="flex items-center gap-3">
                  <div
                    class="bg-primary/20 flex h-10 w-10 items-center justify-center rounded-full">
                    <Sparkles class="text-primary h-5 w-5" />
                  </div>
                  <div>
                    <h3 class="font-semibold">Apply with Hirli AI</h3>
                    <p class="text-muted-foreground text-sm">
                      Increase your chances of getting hired
                    </p>
                  </div>
                </div>
                <Button
                  size="sm"
                  onclick={applyToJob}
                  class={isJobApplied
                    ? 'bg-green-600 hover:bg-green-700'
                    : 'bg-primary hover:bg-primary/90'}>
                  {#if isJobApplied}
                    <ClipboardCheck class="mr-1 h-3.5 w-3.5" />
                    View Application
                  {:else}
                    <Sparkles class="mr-1 h-3.5 w-3.5" />
                    Apply with AI
                  {/if}
                </Button>
              </div>
            </div>
            <div class="bg-primary/5 p-4">
              <ul class="grid gap-2 text-sm md:grid-cols-2">
                <li class="flex items-start gap-2">
                  <CheckCircle class="text-primary mt-0.5 h-4 w-4" />
                  <span>AI-optimized resume tailored for this job</span>
                </li>
                <li class="flex items-start gap-2">
                  <CheckCircle class="text-primary mt-0.5 h-4 w-4" />
                  <span>Keyword matching for ATS systems</span>
                </li>
                <li class="flex items-start gap-2">
                  <CheckCircle class="text-primary mt-0.5 h-4 w-4" />
                  <span>Highlight relevant skills and experience</span>
                </li>
                <li class="flex items-start gap-2">
                  <CheckCircle class="text-primary mt-0.5 h-4 w-4" />
                  <span>Professional formatting and layout</span>
                </li>
              </ul>
            </div>
          </div>

          <Tabs.Root>
            <Tabs.List class="w-full border-b">
              <Tabs.Trigger value="description" class="flex-1">Description</Tabs.Trigger>
              <Tabs.Trigger value="requirements" class="flex-1">Requirements</Tabs.Trigger>
              <Tabs.Trigger value="benefits" class="flex-1">Benefits</Tabs.Trigger>
            </Tabs.List>
            <Tabs.Content value="description" class="mt-4">
              <div class="prose max-w-none">
                {@html job.description || 'No description available.'}
              </div>
            </Tabs.Content>
            <Tabs.Content value="requirements" class="mt-4">
              {#if job.requirements && job.requirements.length > 0}
                <ul class="list-disc space-y-2 pl-5">
                  {#each job.requirements as requirement}
                    <li>{requirement}</li>
                  {/each}
                </ul>
              {:else}
                <p>No specific requirements listed.</p>
              {/if}
            </Tabs.Content>
            <Tabs.Content value="benefits" class="mt-4">
              {#if job.benefits && job.benefits.length > 0}
                <ul class="list-disc space-y-2 pl-5">
                  {#each job.benefits as benefit}
                    <li>{benefit}</li>
                  {/each}
                </ul>
              {:else}
                <p>No benefits listed.</p>
              {/if}
            </Tabs.Content>
          </Tabs.Root>
        </Card.Content>

        <Card.Footer class="flex justify-center p-6">
          <Button
            onclick={applyToJob}
            class={isJobApplied
              ? 'bg-green-600 px-8 hover:bg-green-700'
              : 'bg-primary hover:bg-primary/90 px-8'}>
            {#if isJobApplied}
              <ClipboardCheck class="mr-2 h-4 w-4" />
              View Application
            {:else}
              <Sparkles class="mr-2 h-4 w-4" />
              Apply Now
            {/if}
          </Button>
        </Card.Footer>
      </Card.Root>

      <!-- About the company section -->
      {#if job.company}
        <Card.Root class="mt-6">
          <Card.Header class="p-6">
            <Card.Title class="flex items-center gap-2">
              <Building class="h-5 w-5" />
              About the company
            </Card.Title>
          </Card.Header>
          <Card.Content class="p-6 pt-0">
            <div class="flex items-center gap-4">
              <div
                class="bg-primary/10 text-primary flex h-16 w-16 items-center justify-center rounded-md">
                <Building class="h-8 w-8" />
              </div>
              <div>
                <h3 class="text-lg font-semibold">{job.company}</h3>
                <p class="text-muted-foreground text-sm">
                  {job.location || 'Remote'}
                </p>
              </div>
              <Button variant="outline" size="sm" class="ml-auto">Follow</Button>
            </div>
            <p class="mt-4 text-sm">
              {`${job.company} is hiring for the role of ${job.title}. Apply now to join their team.`}
            </p>
          </Card.Content>
        </Card.Root>
      {/if}
    </div>

    <!-- Sidebar (right side) -->
    <div class="space-y-6">
      <!-- AI Match Analysis card -->
      {#if skillMatchData}
        <Card.Root class="overflow-hidden">
          <Card.Header class="bg-primary/5 p-4">
            <div class="flex items-center justify-between">
              <Card.Title class="flex items-center gap-2">
                <Sparkles class="text-primary h-5 w-5" />
                AI Match Analysis
              </Card.Title>
              <Badge
                class={`${getScoreColorClass(skillMatchData.overallMatch)} px-2.5 py-1 text-base`}>
                {formatMatchScore(skillMatchData.overallMatch)}
              </Badge>
            </div>
            <Card.Description>How your profile matches this job</Card.Description>
          </Card.Header>
          <Card.Content class="p-4">
            <!-- Overall match visualization -->
            <div class="mb-6 flex flex-col items-center">
              <div class="relative mb-2 h-32 w-32">
                <svg class="h-32 w-32 -rotate-90 transform" viewBox="0 0 100 100">
                  <!-- Background circle -->
                  <circle
                    class="stroke-gray-200"
                    cx="50"
                    cy="50"
                    r="45"
                    fill="none"
                    stroke-width="10" />
                  <!-- Progress circle -->
                  <circle
                    class={getProgressColorClass(skillMatchData.overallMatch)}
                    cx="50"
                    cy="50"
                    r="45"
                    fill="none"
                    stroke-width="10"
                    stroke-dasharray="282.7"
                    stroke-dashoffset={282.7 - 282.7 * skillMatchData.overallMatch} />
                </svg>
                <div class="absolute inset-0 flex items-center justify-center">
                  <span class="text-2xl font-bold"
                    >{formatMatchScore(skillMatchData.overallMatch)}</span>
                </div>
              </div>
              <p class="text-muted-foreground text-center text-sm">Overall Match Score</p>
            </div>

            <!-- Match breakdown -->
            <div class="space-y-4">
              <div>
                <div class="mb-1 flex items-center justify-between text-sm">
                  <div class="flex items-center gap-2">
                    <div class="h-3 w-3 rounded-full bg-blue-500"></div>
                    <span>Skills Match</span>
                  </div>
                  <span class="font-medium">{formatMatchScore(skillMatchData.skillsMatch)}</span>
                </div>
                <div class="h-2 rounded-full bg-gray-200">
                  <div
                    class="h-2 rounded-full bg-blue-500"
                    style="width: {Math.min(skillMatchData.skillsMatch * 100, 100)}%">
                  </div>
                </div>
              </div>
              <div>
                <div class="mb-1 flex items-center justify-between text-sm">
                  <div class="flex items-center gap-2">
                    <div class="h-3 w-3 rounded-full bg-green-500"></div>
                    <span>Experience Match</span>
                  </div>
                  <span class="font-medium"
                    >{formatMatchScore(skillMatchData.experienceMatch)}</span>
                </div>
                <div class="h-2 rounded-full bg-gray-200">
                  <div
                    class="h-2 rounded-full bg-green-500"
                    style="width: {Math.min(skillMatchData.experienceMatch * 100, 100)}%">
                  </div>
                </div>
              </div>
              <div>
                <div class="mb-1 flex items-center justify-between text-sm">
                  <div class="flex items-center gap-2">
                    <div class="h-3 w-3 rounded-full bg-purple-500"></div>
                    <span>Education Match</span>
                  </div>
                  <span class="font-medium">{formatMatchScore(skillMatchData.educationMatch)}</span>
                </div>
                <div class="h-2 rounded-full bg-gray-200">
                  <div
                    class="h-2 rounded-full bg-purple-500"
                    style="width: {Math.min(skillMatchData.educationMatch * 100, 100)}%">
                  </div>
                </div>
              </div>
            </div>

            <!-- Matched skills -->
            <div class="mt-6 rounded-lg border bg-green-50/50 p-3">
              <h4 class="mb-2 flex items-center gap-1.5 text-sm font-medium text-green-700">
                <CheckCircle class="h-4 w-4 text-green-600" />
                Your matching skills
              </h4>
              <div class="flex flex-wrap gap-2">
                {#each skillMatchData.matchedSkills as skill}
                  <Badge variant="outline" class="border-green-200 bg-green-100/50 text-green-800">
                    {skill.name}
                    <span class="ml-1 text-xs opacity-70">({skill.level})</span>
                  </Badge>
                {/each}
              </div>
            </div>

            <!-- Missing skills -->
            {#if skillMatchData.missingSkills && skillMatchData.missingSkills.length > 0}
              <div class="mt-3 rounded-lg border bg-yellow-50/50 p-3">
                <h4 class="mb-2 flex items-center gap-1.5 text-sm font-medium text-yellow-700">
                  <XCircle class="h-4 w-4 text-yellow-600" />
                  Skills to develop
                </h4>
                <div class="flex flex-wrap gap-2">
                  {#each skillMatchData.missingSkills as skill}
                    <Badge
                      variant="outline"
                      class="border-yellow-200 bg-yellow-100/50 text-yellow-800">
                      {skill.name}
                      <span class="ml-1 text-xs opacity-70">({skill.importance})</span>
                    </Badge>
                  {/each}
                </div>
              </div>
            {/if}

            <!-- Improve match button -->
            <Button class="mt-4 w-full" variant="outline">
              <Sparkles class="mr-2 h-4 w-4" />
              Optimize your resume for this job
            </Button>
          </Card.Content>
        </Card.Root>
      {/if}

      <!-- Similar jobs -->
      {#if similarJobs && similarJobs.length > 0}
        <Card.Root>
          <Card.Header class="bg-muted/5 p-4">
            <Card.Title class="flex items-center gap-2">
              <Briefcase class="h-5 w-5" />
              Similar Jobs
            </Card.Title>
            <Card.Description>Jobs that match your profile and interests</Card.Description>
          </Card.Header>
          <Card.Content class="p-4 pt-0">
            <div class="space-y-3">
              {#each similarJobs as similarJob}
                <a
                  href="/dashboard/jobs/{similarJob.id}"
                  class="hover:border-primary/30 hover:bg-primary/5 group relative block rounded-lg border p-3 transition-colors">
                  <div class="flex justify-between">
                    <h4 class="group-hover:text-primary line-clamp-1 font-medium">
                      {similarJob.title}
                    </h4>
                    {#if similarJob.matchPercentage}
                      <Badge
                        variant="outline"
                        class={getScoreColorClass(similarJob.matchPercentage / 100)}>
                        <Sparkles class="mr-1 h-3 w-3" />
                        {similarJob.matchPercentage}% Match
                      </Badge>
                    {/if}
                  </div>
                  <div class="flex items-center gap-2">
                    <p class="line-clamp-1 text-sm text-gray-600">{similarJob.company}</p>
                    {#if similarJob.salary}
                      <span class="text-xs text-gray-500">• {similarJob.salary}</span>
                    {/if}
                  </div>
                  <div class="mt-2 flex items-center justify-between">
                    <div class="flex items-center gap-1 text-xs text-gray-500">
                      <MapPin class="h-3 w-3" />
                      <span>{similarJob.location || 'Remote'}</span>
                    </div>
                    {#if similarJob.postedDate}
                      <span class="text-xs text-gray-500">
                        {getTimeSincePosted(similarJob.postedDate)}
                      </span>
                    {/if}
                  </div>
                  <div
                    class="group-hover:border-primary/30 absolute inset-0 rounded-lg border-2 border-transparent transition-colors">
                  </div>
                </a>
              {/each}
              <Button variant="outline" class="w-full" asChild>
                <a href="/dashboard/jobs">
                  <Sparkles class="mr-2 h-4 w-4" />
                  Find more matching jobs
                </a>
              </Button>
            </div>
          </Card.Content>
        </Card.Root>
      {/if}
    </div>
  </div>
</div>

<!-- Report Job Dialog -->
<Dialog.Root bind:open={showReportDialog}>
  <Dialog.Overlay />
  <Dialog.Content class="sm:max-w-md">
    <Dialog.Header>
      <Dialog.Title>Report Job</Dialog.Title>
      <Dialog.Description>
        Help us maintain quality job listings by reporting issues with this job.
      </Dialog.Description>
    </Dialog.Header>

    <form
      onsubmit={(e) => {
        e.preventDefault();
        reportJob();
      }}
      class="grid gap-4 py-4">
      <div class="grid gap-2">
        <Label>Reason for reporting</Label>
        <Textarea class="min-h-[120px]" bind:value={reportReason} />
        {#if reportReason.trim().length === 0}
          <p class="text-muted-foreground text-xs">
            Please provide details to help our team review this listing.
          </p>
        {/if}
      </div>

      <div class="text-muted-foreground text-xs">
        <p>Common reasons for reporting:</p>
        <ul class="mt-1 list-disc pl-5">
          <li>Job posting is a scam</li>
          <li>Misleading information</li>
          <li>Discriminatory content</li>
          <li>Duplicate listing</li>
        </ul>
      </div>

      <Dialog.Footer class="pt-2 sm:justify-between">
        <Button
          type="button"
          variant="outline"
          onclick={() => {
            showReportDialog = false;
            reportReason = '';
          }}>
          Cancel
        </Button>
        <Button type="submit" variant="destructive" disabled={!reportReason.trim()}>
          Report Job
        </Button>
      </Dialog.Footer>
    </form>
  </Dialog.Content>
</Dialog.Root>

<!-- Apply Confirmation Dialog -->
<AlertDialog.Root bind:open={showApplyDialog}>
  <AlertDialog.Content>
    <AlertDialog.Header>
      <AlertDialog.Title>Did you apply to this job?</AlertDialog.Title>
      <AlertDialog.Description>
        We opened the job posting in a new tab. If you submitted an application, click "Yes" to add
        it to your tracker.
      </AlertDialog.Description>
    </AlertDialog.Header>
    <AlertDialog.Footer>
      <AlertDialog.Cancel>No, I didn't apply</AlertDialog.Cancel>
      <AlertDialog.Action onclick={confirmJobApplication}>Yes, I applied</AlertDialog.Action>
    </AlertDialog.Footer>
  </AlertDialog.Content>
</AlertDialog.Root>
