<script lang="ts">
  import SEO from '$components/shared/SEO.svelte';
  import type { PageData } from './$types';

  // Component imports
  import AlertsList from './AlertsList.svelte';
  import CreateAlertDialog from './CreateAlertDialog.svelte';
  import SavedJobCard from './SavedJobCard.svelte';
  import EmptyState from '$components/shared/EmptyState.svelte';

  // UI component imports
  import * as Tabs from '$lib/components/ui/tabs';
  import { Button } from '$lib/components/ui/button';
  import * as Card from '$lib/components/ui/card';
  import * as Select from '$lib/components/ui/select';
  import * as Tooltip from '$lib/components/ui/tooltip';
  import * as Sheet from '$lib/components/ui/sheet';
  import { Badge } from '$lib/components/ui/badge';
  import { Label } from '$lib/components/ui/label';
  import { Input } from '$lib/components/ui/input';
  import {
    Plus,
    ChevronRight,
    CheckCircle,
    Bookmark,
    Bell,
    AlertCircle,
    RefreshCw,
    Info,
    X,
    Settings,
    Briefcase,
  } from 'lucide-svelte';
  import { goto } from '$app/navigation';
  import { toast } from 'svelte-sonner';

  let { data }: { data: PageData } = $props();

  // Reactive state for filters
  let matchScoreFilter = $state('');
  let locationFilter = $state('');
  let sortOption = $state('match');
  let showFilterSheet = $state(false);
  let pageSize = $state(data.pagination.limit);
  let profileSwitching = $state(false);

  // Calculate max matches based on plan (mock data for now)
  let maxMatches = $derived(() => {
    // This would come from user's plan data
    return 20; // Free plan limit
  });

  // Calculate active filter count
  let activeFilterCount = $derived(() => {
    let count = 0;
    if (matchScoreFilter) count++;
    if (locationFilter) count++;
    if (sortOption !== 'match') count++;
    return count;
  });

  // Filter and sort matches based on current filters
  let filteredMatches = $derived(() => {
    let matches = [...data.matches];

    // Apply match score filter
    if (matchScoreFilter) {
      const minScore = parseInt(matchScoreFilter) / 100;
      matches = matches.filter((match) => match.matchScore >= minScore);
    }

    // Apply location filter
    if (locationFilter) {
      matches = matches.filter((match) => {
        const job = match.job_listing;
        if (locationFilter === 'remote') return job.remoteType === 'Remote';
        if (locationFilter === 'hybrid') return job.remoteType === 'Hybrid';
        if (locationFilter === 'onsite') return job.remoteType === 'On-site';
        return true;
      });
    }

    // Apply sorting
    matches.sort((a, b) => {
      switch (sortOption) {
        case 'newest':
          return (
            new Date(b.job_listing.postedDate).getTime() -
            new Date(a.job_listing.postedDate).getTime()
          );
        case 'salary':
          const aSalary = a.job_listing.salaryMax || a.job_listing.salaryMin || 0;
          const bSalary = b.job_listing.salaryMax || b.job_listing.salaryMin || 0;
          return bSalary - aSalary;
        case 'company':
          return a.job_listing.company.localeCompare(b.job_listing.company);
        case 'match':
        default:
          return b.matchScore - a.matchScore;
      }
    });

    return matches;
  });

  // Filter saved jobs based on search
  let filteredSavedJobs = $derived(() => {
    if (!savedJobsFilter) return savedJobs;

    const filter = savedJobsFilter.toLowerCase();
    return savedJobs.filter((savedJob: any) => {
      const job = savedJob.job_listing;
      if (!job) {
        console.log('No job_listing found for savedJob:', savedJob);
        return false;
      }

      const titleMatch = job.title?.toLowerCase().includes(filter);
      const companyMatch = job.company?.toLowerCase().includes(filter);
      const locationMatch = job.location?.toLowerCase().includes(filter);

      return titleMatch || companyMatch || locationMatch;
    });
  });

  // Valid tab values
  const VALID_TABS = ['matches', 'saved', 'alerts'];

  // Get the active tab from the URL
  let activeTab = $state('matches');

  if (typeof window !== 'undefined') {
    const url = new URL(window.location.href);
    const tabParam = url.searchParams.get('tab');
    if (tabParam && VALID_TABS.includes(tabParam)) {
      activeTab = tabParam;
    }
  }

  // Saved jobs state
  let savedJobs = $state(data.savedJobs || []);
  let savedJobsError = $state(null);
  let savedJobsLoading = $state(false);
  let savedJobsFilter = $state('');

  // Alert dialog state
  let showCreateAlertDialog = $state(false);

  // SEO metadata based on active tab
  function getPageTitle(tab: string): string {
    switch (tab) {
      case 'saved':
        return 'Saved Jobs';
      case 'alerts':
        return 'Job Alerts';
      default:
        return 'Job Matches';
    }
  }

  function getPageDescription(tab: string): string {
    switch (tab) {
      case 'saved':
        return 'View and manage your saved job opportunities';
      case 'alerts':
        return 'Manage your job alerts and notifications';
      default:
        return 'Jobs matched to your profile based on your resume';
    }
  }

  let pageTitle = $derived(getPageTitle(activeTab));
  let pageDescription = $derived(getPageDescription(activeTab));

  // Track if we've already loaded saved jobs
  let savedJobsLoaded = false;

  // Reset profile switching state when component loads
  if (typeof window !== 'undefined') {
    profileSwitching = false;
  }

  // Job action handlers
  async function handleSaveJob(jobId: string) {
    try {
      const response = await fetch(`/api/jobs/${jobId}/save`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ notes: '' }),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Failed to save job');
      }

      toast.success('Job saved!', {
        description: 'Added to your saved jobs',
      });

      // Refresh saved jobs if we're on the saved tab
      if (activeTab === 'saved') {
        fetchSavedJobs();
      }
    } catch (error) {
      console.error('Error saving job:', error);
      toast.error('Failed to save job');
    }
  }

  async function handleDismissJob(jobId: string) {
    try {
      const response = await fetch(`/api/jobs/${jobId}/dismiss`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Failed to dismiss job');
      }

      // Remove the job from the local matches array
      data.matches = data.matches.filter((match) => match.job_listing.id !== jobId);

      toast.success('Job dismissed', {
        description: 'This job will no longer appear in your matches',
      });
    } catch (error) {
      console.error('Error dismissing job:', error);
      toast.error('Failed to dismiss job');
    }
  }

  function handleTabChange(value: string) {
    activeTab = value;

    // Only fetch saved jobs if we haven't loaded them yet
    if (value === 'saved' && !savedJobsLoaded) {
      fetchSavedJobs();
      // Note: savedJobsLoaded is set in fetchSavedJobs
    }

    updateUrlWithTab(value);
  }

  function handleCreateAlert() {
    showCreateAlertDialog = true;
  }

  function handleAlertCreated() {
    showCreateAlertDialog = false;
    if (typeof window !== 'undefined') {
      window.location.reload();
    }
  }

  function handleAlertDialogClose() {
    showCreateAlertDialog = false;
  }

  // API calls
  async function fetchSavedJobs() {
    savedJobsError = null;
    savedJobsLoading = true;

    try {
      const response = await fetch('/api/saved-jobs');
      if (!response.ok) {
        throw new Error('Failed to fetch saved jobs');
      }

      const result = await response.json();
      savedJobs = result.savedJobs || [];
      // Mark as loaded successfully
      savedJobsLoaded = true;
    } catch (error) {
      console.error('Error fetching saved jobs:', error);
      savedJobsError = error.message;
      // Reset the loaded flag so we can try again
      savedJobsLoaded = false;
    } finally {
      savedJobsLoading = false;
    }
  }

  async function removeSavedJob(jobId: string) {
    try {
      const response = await fetch(`/api/jobs/${jobId}/save`, {
        method: 'DELETE',
      });

      if (response.ok) {
        // Update local state instead of fetching all saved jobs again
        savedJobs = savedJobs.filter((job) => job.jobId !== jobId);
      } else {
        const result = await response.json();
        throw new Error(result.error || 'Failed to remove job');
      }
    } catch (error) {
      console.error('Error removing saved job:', error);
      // Could show a toast notification here
    }
  }

  // Helper functions
  function updateUrlWithTab(tab: string) {
    if (typeof window !== 'undefined') {
      const url = new URL(window.location.href);
      url.searchParams.set('tab', tab);
      window.history.replaceState({}, '', url.toString());
    }
  }
</script>

<SEO
  title="{pageTitle} | Hirli"
  description={pageDescription}
  keywords="job matches, saved jobs, job alerts, AI recommendations, career matches, job opportunities, skill matching, resume matching" />

<!-- Tabs for different sections -->
<Tabs.Root value={activeTab} onValueChange={handleTabChange}>
  <div class="p-0">
    <Tabs.List class="border-t-0">
      <Tabs.Trigger value="matches" class="flex-1 gap-2">
        <CheckCircle class="h-4 w-4" />
        <span>Job Matches</span>
      </Tabs.Trigger>
      <Tabs.Trigger value="saved" class="flex-1 gap-2">
        <Bookmark class="h-4 w-4" />
        <span>Saved Jobs</span>
      </Tabs.Trigger>
      <Tabs.Trigger value="alerts" class="flex-1 gap-2">
        <Bell class="h-4 w-4" />
        <span>Job Alerts</span>
      </Tabs.Trigger>
    </Tabs.List>
  </div>

  <!-- Matches Tab -->
  <Tabs.Content value="matches" class="p-4">
    <!-- Filter Button and Results -->
    <div class="mb-6 flex items-center justify-between">
      <!-- Filter Button and Results Badge -->
      <div class="flex items-center gap-4">
        <Button
          variant="outline"
          onclick={() => (showFilterSheet = true)}
          class="relative flex items-center gap-2">
          <Settings class="h-4 w-4" />
          Filters
          {#if activeFilterCount() > 0}
            <Badge variant="default" class="ml-1 h-5 w-5 rounded-full p-0 text-xs">
              {activeFilterCount()}
            </Badge>
          {/if}
        </Button>

        <!-- Results Badge with Info Tooltip -->
        <div class="flex items-center gap-2">
          <Badge variant="secondary" class="text-sm">
            {filteredMatches().length} matches found
          </Badge>
          <Tooltip.Provider>
            <Tooltip.Root>
              <Tooltip.Trigger>
                <Info class="text-muted-foreground h-4 w-4 cursor-help" />
              </Tooltip.Trigger>
              <Tooltip.Content>
                <p>Free plan: {maxMatches()} matches per day</p>
              </Tooltip.Content>
            </Tooltip.Root>
          </Tooltip.Provider>
        </div>
      </div>

      <!-- Profile Selector -->
      <div class="flex items-center gap-2">
        <Label class="text-muted-foreground text-sm font-medium">Profile:</Label>
        <Select.Root
          type="single"
          value={data.selectedProfileId}
          disabled={profileSwitching}
          onValueChange={(profileId: string) => {
            if (profileId && profileId !== data.selectedProfileId) {
              profileSwitching = true;
              // Navigate to update matches with new profile
              goto(`/dashboard/matches?profileId=${profileId}&page=1&limit=${pageSize}`);
            }
          }}>
          <Select.Trigger class="w-50 px-3 py-2">
            <div class="flex items-center gap-2">
              {#if profileSwitching}
                <RefreshCw class="h-4 w-4 animate-spin" />
              {/if}
              <Select.Value
                placeholder={data.profiles.find((profile) => profile.id === data.selectedProfileId)
                  ?.name || 'Select Profile'} />
            </div>
          </Select.Trigger>
          <Select.Content class="max-h-60">
            {#each data.profiles as profile}
              <Select.Item value={profile.id}>{profile.name}</Select.Item>
            {/each}
          </Select.Content>
        </Select.Root>
      </div>
    </div>

    {#if data.profiles.length === 0}
      <div class="rounded-lg border p-6">
        <EmptyState
          title="No profiles found"
          description="Create a profile to get job matches based on your resume."
          actionText="Create Profile"
          actionHref="/dashboard/settings/profile" />
      </div>
    {:else if filteredMatches().length === 0}
      <div class="rounded-lg border p-6">
        <EmptyState
          title={data.matches.length === 0
            ? 'No job matches available'
            : 'No matches with current filters'}
          description={data.matches.length === 0
            ? 'We are continuously adding new job opportunities. Check back soon or browse all available jobs.'
            : 'Try adjusting your filters to see more job matches. You have ' +
              data.matches.length +
              ' total matches available.'}
          actionText={data.matches.length === 0 ? 'Browse All Jobs' : 'Clear Filters'}
          actionHref={data.matches.length === 0 ? '/dashboard/jobs' : '#'}
          on:click={data.matches.length === 0
            ? undefined
            : () => {
                matchScoreFilter = '';
                locationFilter = '';
                sortOption = 'match';
              }} />
      </div>
    {:else}
      <!-- Job Matches Grid -->
      <div class="grid gap-4 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
        {#each filteredMatches() as match}
          {@const job = match.job_listing}
          {@const matchScore = Math.round(match.matchScore * 100)}
          {@const scoreColor =
            matchScore >= 90
              ? 'bg-green-100 text-green-800 border-green-200'
              : matchScore >= 80
                ? 'bg-blue-100 text-blue-800 border-blue-200'
                : matchScore >= 70
                  ? 'bg-yellow-100 text-yellow-800 border-yellow-200'
                  : 'bg-gray-100 text-gray-800 border-gray-200'}

          <Card.Root
            class="hover:border-primary/50 group relative gap-0 p-0 transition-all hover:shadow-sm">
            <!-- Top Right Action Icons -->
            <div
              class="absolute right-2 top-2 z-10 flex gap-1"
              role="group"
              aria-label="Job actions">
              <Tooltip.Provider>
                <Tooltip.Root>
                  <Tooltip.Trigger>
                    <Button
                      size="sm"
                      variant="ghost"
                      class="hover:bg-background/80 h-6 w-6 p-0"
                      onclick={() => handleSaveJob(job.id)}>
                      <Bookmark class="h-3 w-3" />
                    </Button>
                  </Tooltip.Trigger>
                  <Tooltip.Content>
                    <p>Save job</p>
                  </Tooltip.Content>
                </Tooltip.Root>
              </Tooltip.Provider>

              <Tooltip.Provider>
                <Tooltip.Root>
                  <Tooltip.Trigger>
                    <Button
                      size="sm"
                      variant="ghost"
                      class="hover:bg-background/80 h-6 w-6 p-0"
                      onclick={() => handleDismissJob(job.id)}>
                      <X class="h-3 w-3" />
                    </Button>
                  </Tooltip.Trigger>
                  <Tooltip.Content>
                    <p>Dismiss job</p>
                  </Tooltip.Content>
                </Tooltip.Root>
              </Tooltip.Provider>
            </div>

            <!-- Match Score Badge with Tooltip -->
            <div class="absolute -left-2 -top-2 z-10">
              <Tooltip.Provider>
                <Tooltip.Root>
                  <Tooltip.Trigger>
                    <Badge
                      class="border-background rounded-full border-2 px-2 py-1 text-xs font-bold {scoreColor}">
                      {matchScore}%
                    </Badge>
                  </Tooltip.Trigger>
                  <Tooltip.Content class="max-w-xs">
                    <div class="space-y-1">
                      <p class="font-medium">Why this matches:</p>
                      <ul class="space-y-0.5 text-xs">
                        <li>• Skills match: {Math.round(matchScore * 0.8)}%</li>
                        <li>• Experience level: {job.experienceLevel || 'Mid-level'}</li>
                        <li>• Location preference: {job.remoteType || 'On-site'}</li>
                        {#if job.techStack && job.techStack.length > 0}
                          <li>• Tech stack: {job.techStack.slice(0, 2).join(', ')}</li>
                        {/if}
                      </ul>
                    </div>
                  </Tooltip.Content>
                </Tooltip.Root>
              </Tooltip.Provider>
            </div>
            <Card.Header class="border-border gap-1 border-b !p-4">
              <Card.Title class="flex items-center gap-2">
                <Briefcase class="h-5 w-5" />
                {job.title}
              </Card.Title>
              <Card.Description>
                {job.company}
              </Card.Description>
            </Card.Header>
            <Card.Content class="flex flex-col gap-2 p-4">
              <!-- Job Content -->
              <!-- Key Benefits/Highlights -->
              {#if job.benefits && job.benefits.length > 0}
                <div class="flex flex-wrap gap-1">
                  {#each job.benefits.slice(0, 3) as benefit}
                    <Badge variant="outline" class="px-1.5 py-0.5 text-xs">
                      {benefit}
                    </Badge>
                  {/each}
                </div>
              {/if}

              <div class="text-muted-foreground space-y-1 text-xs">
                <p class="flex items-center gap-1">
                  <span class="inline-block h-1 w-1 rounded-full bg-current"></span>
                  {job.location}
                </p>
                {#if job.salaryMin && job.salaryMax}
                  <p class="flex items-center gap-1">
                    <span class="inline-block h-1 w-1 rounded-full bg-current"></span>
                    ${job.salaryMin?.toLocaleString()} - ${job.salaryMax?.toLocaleString()}
                  </p>
                {:else if job.salary}
                  <p class="flex items-center gap-1">
                    <span class="inline-block h-1 w-1 rounded-full bg-current"></span>
                    {job.salary}
                  </p>
                {/if}
                {#if job.employmentType}
                  <p class="flex items-center gap-1">
                    <span class="inline-block h-1 w-1 rounded-full bg-current"></span>
                    {job.employmentType}
                  </p>
                {/if}
                {#if job.remoteType}
                  <p class="flex items-center gap-1">
                    <span class="inline-block h-1 w-1 rounded-full bg-current"></span>
                    {job.remoteType}
                  </p>
                {/if}
              </div>
            </Card.Content>

            <Card.Footer class="border-t !p-2">
              <Button
                size="sm"
                class="h-8 w-full text-xs"
                onclick={() => goto(`/dashboard/jobs/${job.id}`)}>
                Apply Now
              </Button>
            </Card.Footer>
          </Card.Root>
        {/each}
      </div>

      <!-- Pagination -->
      {#if data.pagination.totalPages > 1}
        <div class="mt-8 flex items-center justify-between">
          <!-- Results info and page size selector -->
          <div class="flex items-center gap-4">
            <div class="text-muted-foreground text-sm">
              Showing {(data.pagination.page - 1) * data.pagination.limit + 1} to {Math.min(
                data.pagination.page * data.pagination.limit,
                data.pagination.totalCount
              )} of {data.pagination.totalCount} results
            </div>

            <!-- Page size selector -->
            <div class="flex items-center gap-2">
              <span class="text-muted-foreground text-sm">Show:</span>
              <Select.Root
                type="single"
                value={String(pageSize)}
                onValueChange={(value) => {
                  const newPageSize = parseInt(value || '20');
                  pageSize = newPageSize;
                  goto(
                    `/dashboard/matches?profileId=${data.selectedProfileId}&page=1&limit=${newPageSize}`
                  );
                }}>
                <Select.Trigger class="h-8 w-16">
                  <Select.Value placeholder={String(pageSize)} />
                </Select.Trigger>
                <Select.Content>
                  <Select.Item value="10">10</Select.Item>
                  <Select.Item value="20">20</Select.Item>
                  <Select.Item value="50">50</Select.Item>
                  <Select.Item value="100">100</Select.Item>
                </Select.Content>
              </Select.Root>
            </div>
          </div>

          <!-- Pagination controls -->
          <div class="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              disabled={data.pagination.page === 1}
              onclick={() =>
                goto(
                  `/dashboard/matches?profileId=${data.selectedProfileId}&page=1&limit=${pageSize}`
                )}>
              First
            </Button>
            <Button
              variant="outline"
              size="sm"
              disabled={data.pagination.page === 1}
              onclick={() =>
                goto(
                  `/dashboard/matches?profileId=${data.selectedProfileId}&page=${data.pagination.page - 1}&limit=${pageSize}`
                )}>
              Previous
            </Button>

            <!-- Page numbers -->
            <div class="flex items-center gap-1">
              {#each Array.from({ length: Math.min(5, data.pagination.totalPages) }, (_, i) => {
                const startPage = Math.max(1, data.pagination.page - 2);
                return startPage + i;
              }).filter((page) => page <= data.pagination.totalPages) as page}
                <Button
                  variant={page === data.pagination.page ? 'default' : 'outline'}
                  size="sm"
                  class="h-8 w-8 p-0"
                  onclick={() =>
                    goto(
                      `/dashboard/matches?profileId=${data.selectedProfileId}&page=${page}&limit=${pageSize}`
                    )}>
                  {page}
                </Button>
              {/each}
            </div>

            <Button
              variant="outline"
              size="sm"
              disabled={!data.pagination.hasMore}
              onclick={() =>
                goto(
                  `/dashboard/matches?profileId=${data.selectedProfileId}&page=${data.pagination.page + 1}&limit=${pageSize}`
                )}>
              Next
            </Button>
            <Button
              variant="outline"
              size="sm"
              disabled={!data.pagination.hasMore}
              onclick={() =>
                goto(
                  `/dashboard/matches?profileId=${data.selectedProfileId}&page=${data.pagination.totalPages}&limit=${pageSize}`
                )}>
              Last
            </Button>
          </div>
        </div>
      {/if}
    {/if}
  </Tabs.Content>

  <!-- Saved Jobs Tab -->
  <Tabs.Content value="saved" class="p-4">
    <div class="mb-6 flex items-center justify-between">
      <div class="flex items-center gap-4">
        <h2 class="text-lg font-semibold">Your Saved Jobs</h2>
        <Badge variant="secondary" class="text-sm">
          {savedJobs.length} saved
        </Badge>
        {#if savedJobsFilter}
          <Badge variant="outline" class="text-sm">
            {filteredSavedJobs().length} filtered
          </Badge>
        {/if}
      </div>
      <div class="flex gap-2">
        <Button
          variant="outline"
          onclick={fetchSavedJobs}
          disabled={savedJobsLoading}
          class="flex items-center gap-2">
          <RefreshCw class="h-4 w-4 {savedJobsLoading ? 'animate-spin' : ''}" />
          <span>Refresh</span>
        </Button>
        <Button
          variant="outline"
          onclick={() => goto('/dashboard/jobs')}
          class="flex items-center gap-2">
          <ChevronRight class="h-4 w-4" />
          <span>Browse Jobs</span>
        </Button>
      </div>
    </div>

    <!-- Search filter -->
    {#if savedJobs.length > 0}
      <div class="mb-4">
        <Input
          type="text"
          placeholder="Search saved jobs by title, company, or location..."
          bind:value={savedJobsFilter}
          class="max-w-md" />
      </div>

      <!-- Debug info (remove in production) -->
      <div class="mb-4 rounded-lg bg-gray-50 p-3 text-xs">
        <p><strong>Debug Info:</strong></p>
        <p>Total saved jobs: {savedJobs.length}</p>
        <p>Filtered results: {filteredSavedJobs().length}</p>
        <p>Search term: "{savedJobsFilter}"</p>
        {#if savedJobs.length > 0}
          <details class="mt-2">
            <summary class="cursor-pointer font-medium">Sample job data</summary>
            <pre class="mt-2 overflow-auto text-xs">{JSON.stringify(savedJobs[0], null, 2)}</pre>
          </details>
        {:else}
          <p class="text-red-600">No saved jobs found in data</p>
          {#if data.matches.length > 0}
            <Button
              size="sm"
              class="mt-2"
              onclick={() => handleSaveJob(data.matches[0].job_listing.id)}>
              Test: Save First Match
            </Button>
          {/if}
        {/if}
      </div>
    {/if}

    <!-- Error state -->
    {#if savedJobsError}
      <div class="rounded-lg border border-red-200 bg-red-50 p-6">
        <div class="flex flex-col items-center space-y-2 text-center">
          <AlertCircle class="h-6 w-6 text-red-500" />
          <h3 class="text-lg font-medium text-red-800">Error loading saved jobs</h3>
          <p class="text-sm text-red-600">{savedJobsError}</p>
          <Button variant="outline" class="mt-4" onclick={fetchSavedJobs}>Try Again</Button>
        </div>
      </div>

      <!-- Jobs list -->
    {:else if savedJobs.length > 0}
      {#if filteredSavedJobs().length > 0}
        <div class="grid gap-4 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
          {#each filteredSavedJobs() as savedJob}
            {@const job = (savedJob as any).job_listing}
            {#if job}
              <SavedJobCard {job} on:remove={(e) => removeSavedJob(e.detail.jobId)} />
            {/if}
          {/each}
        </div>
      {:else}
        <!-- No results for filter -->
        <div class="rounded-lg border p-6 text-center">
          <div class="text-muted-foreground">
            <p class="text-lg font-medium">No jobs match your search</p>
            <p class="mt-1 text-sm">Try adjusting your search terms or clear the filter.</p>
            <Button variant="outline" class="mt-4" onclick={() => (savedJobsFilter = '')}>
              Clear Search
            </Button>
          </div>
        </div>
      {/if}

      <!-- Empty state -->
    {:else}
      <EmptyState
        title="No saved jobs"
        description="Save jobs that interest you to keep track of opportunities."
        actionText="Browse Jobs"
        actionHref="/dashboard/jobs" />
    {/if}
  </Tabs.Content>

  <!-- Alerts Tab -->
  <Tabs.Content value="alerts" class="p-4">
    <div class="mb-6 flex items-center justify-between">
      <div class="flex items-center gap-4">
        <h2 class="text-lg font-semibold">Your Job Alerts</h2>
        <Badge variant="secondary" class="text-sm">
          {data.jobAlerts?.length || 0} alerts
        </Badge>
        {#if data.jobAlerts?.filter((alert) => alert.enabled).length}
          <Badge variant="default" class="text-sm">
            {data.jobAlerts.filter((alert) => alert.enabled).length} active
          </Badge>
        {/if}
      </div>
      <Button variant="default" onclick={handleCreateAlert} class="flex items-center gap-2">
        <Plus class="h-4 w-4" />
        <span>Create Alert</span>
      </Button>
    </div>
    <div class="rounded-lg border p-6">
      <AlertsList alerts={data.jobAlerts || []} onCreateAlert={handleCreateAlert} />
    </div>
  </Tabs.Content>
</Tabs.Root>

<!-- Filter Sheet -->
<Sheet.Root bind:open={showFilterSheet}>
  <Sheet.Content side="left" class="w-[400px] sm:w-[540px]">
    <Sheet.Header class="border-b p-4">
      <Sheet.Title>Filter Jobs</Sheet.Title>
      <Sheet.Description>
        Refine your job matches with filters and sorting options.
      </Sheet.Description>
    </Sheet.Header>

    <div class="space-y-6 p-4">
      <!-- Match Score Filter -->
      <div class="space-y-2">
        <Label class="text-sm font-medium">Match Score</Label>
        <Select.Root
          type="single"
          value={matchScoreFilter}
          onValueChange={(value) => {
            matchScoreFilter = value || '';
          }}>
          <Select.Trigger>
            <Select.Value placeholder="All Matches" />
          </Select.Trigger>
          <Select.Content>
            <Select.Item value="">All Matches</Select.Item>
            <Select.Item value="90">90%+ (Excellent)</Select.Item>
            <Select.Item value="80">80-89% (Great)</Select.Item>
            <Select.Item value="70">70-79% (Good)</Select.Item>
            <Select.Item value="60">60-69% (Fair)</Select.Item>
          </Select.Content>
        </Select.Root>
      </div>

      <!-- Location Filter -->
      <div class="space-y-2">
        <Label class="text-sm font-medium">Location Type</Label>
        <Select.Root
          type="single"
          value={locationFilter}
          onValueChange={(value) => {
            locationFilter = value || '';
          }}>
          <Select.Trigger>
            <Select.Value placeholder="All Locations" />
          </Select.Trigger>
          <Select.Content>
            <Select.Item value="">All Locations</Select.Item>
            <Select.Item value="remote">Remote</Select.Item>
            <Select.Item value="hybrid">Hybrid</Select.Item>
            <Select.Item value="onsite">On-site</Select.Item>
          </Select.Content>
        </Select.Root>
      </div>

      <!-- Sort Options -->
      <div class="space-y-2">
        <Label class="text-sm font-medium">Sort By</Label>
        <Select.Root
          type="single"
          value={sortOption}
          onValueChange={(value) => {
            sortOption = value || 'match';
          }}>
          <Select.Trigger>
            <Select.Value placeholder="Best Match" />
          </Select.Trigger>
          <Select.Content>
            <Select.Item value="match">Best Match</Select.Item>
            <Select.Item value="newest">Newest First</Select.Item>
            <Select.Item value="salary">Highest Salary</Select.Item>
            <Select.Item value="company">Company A-Z</Select.Item>
          </Select.Content>
        </Select.Root>
      </div>

      <!-- Quick Filters -->
      <div class="space-y-2">
        <Label class="text-sm font-medium">Quick Filters</Label>
        <div class="flex flex-wrap gap-2">
          <Button
            variant={matchScoreFilter === '90' ? 'default' : 'outline'}
            size="sm"
            class="h-8"
            onclick={() => {
              matchScoreFilter = matchScoreFilter === '90' ? '' : '90';
            }}>
            High Match (90%+)
          </Button>
          <Button
            variant={locationFilter === 'remote' ? 'default' : 'outline'}
            size="sm"
            class="h-8"
            onclick={() => {
              locationFilter = locationFilter === 'remote' ? '' : 'remote';
            }}>
            Remote Only
          </Button>
          <Button
            variant="outline"
            size="sm"
            class="h-8"
            onclick={() => {
              // This would filter by tech companies - could be implemented with company tags
              toast.info('Tech company filter coming soon!');
            }}>
            Tech Companies
          </Button>
          <Button
            variant="outline"
            size="sm"
            class="h-8"
            onclick={() => {
              // This would filter by salary range - could be implemented with salary filters
              toast.info('Salary filter coming soon!');
            }}>
            $100k+ Salary
          </Button>
        </div>
      </div>
    </div>

    <Sheet.Footer class="border-t p-2">
      <div class="flex gap-2">
        <Button
          variant="outline"
          onclick={() => {
            matchScoreFilter = '';
            locationFilter = '';
            sortOption = 'match';
          }}>
          Clear All
        </Button>
      </div>
    </Sheet.Footer>
  </Sheet.Content>
</Sheet.Root>

<!-- Create Alert Dialog -->
{#if showCreateAlertDialog}
  <CreateAlertDialog
    onClose={handleAlertDialogClose}
    onCreated={handleAlertCreated}
    userId={data.user?.id} />
{/if}
