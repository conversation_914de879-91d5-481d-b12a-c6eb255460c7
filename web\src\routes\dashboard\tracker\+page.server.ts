import { fail } from '@sveltejs/kit';
import { superValidate } from 'sveltekit-superforms/server';
import { jobApplicationSchema } from '$lib/validators/jobApplication';
import { zod } from 'sveltekit-superforms/adapters';
import { prisma } from '$lib/server/prisma';
import { verifySessionToken } from '$lib/server/auth';

export const load = async ({ cookies }) => {
  // Create an empty form for adding new job applications
  const form = await superValidate(zod(jobApplicationSchema));

  // Get user from session
  const token = cookies.get('auth_token');
  const user = token ? await verifySessionToken(token) : null;

  if (!user) {
    return {
      form,
      applications: [],
    };
  }

  try {
    // Load user's applications from database
    const applications = await prisma.application.findMany({
      where: {
        userId: user.id,
      },
      orderBy: {
        appliedDate: 'desc',
      },
    });

    // Transform applications to match the expected format
    const transformedApplications = applications.map((app) => ({
      id: app.id,
      company: app.company,
      position: app.position,
      location: app.location || 'Remote',
      appliedDate: app.appliedDate.toISOString().split('T')[0], // Format as YYYY-MM-DD
      status: app.status,
      nextAction: app.nextAction || '',
      notes: app.notes || '',
      logo: 'https://placehold.co/100x100', // Default logo for now
      url: app.url || '',
      jobType: app.jobType || 'Full-time',
      resumeUploaded: app.resumeUploaded ? 'Yes' : 'No',
    }));

    return {
      form,
      applications: transformedApplications,
    };
  } catch (error) {
    console.error('Error loading applications:', error);
    return {
      form,
      applications: [],
    };
  }
};

export const actions = {
  addJob: async ({ request, cookies }) => {
    const form = await superValidate(request, zod(jobApplicationSchema));

    if (!form.valid) {
      return fail(400, { form });
    }

    // Get user from session
    const token = cookies.get('auth_token');
    const user = token ? await verifySessionToken(token) : null;

    if (!user) {
      return fail(401, { form, error: 'Unauthorized' });
    }

    try {
      // Create new application in database
      const application = await prisma.application.create({
        data: {
          userId: user.id,
          company: form.data.company,
          position: form.data.position,
          location: form.data.location || null,
          appliedDate: new Date(form.data.appliedDate),
          status: form.data.status,
          nextAction: form.data.nextAction || null,
          notes: form.data.notes || null,
          url: form.data.url || null,
          jobType: form.data.jobType,
          resumeUploaded: form.data.resumeUploaded === 'Yes',
        },
      });

      return {
        form,
        success: true,
        application,
      };
    } catch (error) {
      console.error('Error creating application:', error);
      return fail(500, { form, error: 'Failed to create application' });
    }
  },
};
