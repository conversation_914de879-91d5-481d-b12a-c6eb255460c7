<script lang="ts">
  import { superForm } from 'sveltekit-superforms/client';
  import { zodClient } from 'sveltekit-superforms/adapters';
  import { toast } from 'svelte-sonner';
  import { jobApplicationSchema } from '$lib/validators/jobApplication';
  import { List, LayoutGrid, Download, Plus, Search, Briefcase, CheckCircle } from 'lucide-svelte';
  import { Badge } from '$lib/components/ui/badge';
  import { Button } from '$lib/components/ui/button';
  import * as Tabs from '$lib/components/ui/tabs';
  import SEO from '$components/shared/SEO.svelte';
  import AddJobModal from './components/AddJobModal.svelte';
  import KanbanView from './components/KanbanView.svelte';
  import ApplicationDetailsSheet from './components/ApplicationDetailsSheet.svelte';
  import { Input } from '$lib/components/ui/input';
  import { browser } from '$app/environment';
  import DataTable from './data-table-new.svelte';

  import { statusColors } from './types';

  export let data;

  // Set up the form with validation
  const { form, enhance, reset, errors, constraints, submitting } = superForm(data.form, {
    validators: zodClient(jobApplicationSchema),
    dataType: 'json',
    resetForm: true,
    onResult: ({ result }) => {
      if (result.type === 'success') {
        // Add the new application to the list
        const newApplication = {
          id: result.data?.application?.id || crypto.randomUUID(),
          company: $form.company || '',
          position: $form.position || '',
          location: $form.location || 'Remote',
          appliedDate: $form.appliedDate || '',
          status: $form.status || 'Applied',
          nextAction: $form.nextAction || '',
          notes: $form.notes || '',
          logo: 'https://placehold.co/100x100',
          url: $form.url || '',
          jobType: $form.jobType || 'Full-time',
          resumeUploaded: $form.resumeUploaded || 'No',
        };

        // Update applications array with the new job
        applications = [...applications, newApplication];

        // Close modal and show success message
        newJobModalOpen = false;
        toast.success('New job application added successfully!');
      } else if (result.type === 'failure') {
        toast.error('Failed to add job application. Please try again.');
      }
    },
  });

  console.log(data);

  // Add state for the sheet
  let sheetOpen = false;
  let selectedApplication = null;

  // Add state for the new job modal
  let newJobModalOpen = false;

  // Company options
  const companies = [
    'TechCorp',
    'DataSystems Inc',
    'InnovateTech',
    'CloudSolutions',
    'FinTech Global',
    'Other',
  ];

  // Job type options
  const jobTypes = ['Full-time', 'Part-time', 'Contract', 'Freelance', 'Internship'];

  // Job status options
  const jobStatuses = ['Applied', 'Interview', 'Assessment', 'Offer', 'Rejected'];

  // Resume uploaded options
  const resumeOptions = ['Yes', 'No', 'N/A'];

  // Function to open the sheet with the selected application
  function openApplicationDetails(application: any) {
    selectedApplication = application;
    sheetOpen = true;
  }

  // Use the applications from the server
  let applications: any[] = data.applications || [];

  let activeView = true; // true for active, false for archived
  let searchQuery = '';
  let viewMode = 'kanban'; // 'kanban' or 'list'

  // Status colors and icons are now imported from types.ts

  // Define the search term for filtering
  let searchTerm = '';

  // Define the columns for our Kanban board
  const columns = [
    { id: 'Applied', name: 'Applied' },
    { id: 'Interview', name: 'Interview' },
    { id: 'Assessment', name: 'Assessment' },
    { id: 'Offer', name: 'Offer' },
    { id: 'Rejected', name: 'Rejected' },
  ];

  // Filter applications based on active/archived view and search query
  $: filteredApplications = applications.filter((app) => {
    const isArchived = app.status === 'Rejected' || app.status === 'Offer';
    const matchesView = activeView ? !isArchived : isArchived;
    const matchesSearch =
      searchQuery === '' ||
      app.position.toLowerCase().includes(searchQuery.toLowerCase()) ||
      app.company.toLowerCase().includes(searchQuery.toLowerCase());
    return matchesView && matchesSearch;
  });

  // Group applications by status for Kanban view
  $: groupedApplications = columns.reduce((acc, column) => {
    acc[column.id] = filteredApplications.filter((app) => app.status === column.id);
    return acc;
  }, {});

  // Add some CSS for drag and drop visual feedback, but only in the browser
  $: if (browser) {
    // This will only run in the browser, not during SSR
    const dragStyle = document.createElement('style');
    dragStyle.textContent = `
      .dragging {
        opacity: 0.7;
        transform: scale(0.95);
        box-shadow: 0 10px 15px rgba(0, 0, 0, 0.1);
      }
    `;
    document.head.appendChild(dragStyle);
  }

  // Add state to track export in progress
  let isExporting = false;

  // Export applications as CSV
  async function exportCSV() {
    if (isExporting) return; // Prevent double clicks

    isExporting = true;

    try {
      const headers = [
        'Company',
        'Position',
        'Location',
        'Applied Date',
        'Status',
        'Next Action',
        'Notes',
        'URL',
      ];
      const csvContent = [
        headers.join(','),
        ...applications.map((app) =>
          [
            `"${app.company.replace(/"/g, '""')}"`,
            `"${app.position.replace(/"/g, '""')}"`,
            `"${app.location.replace(/"/g, '""')}"`,
            `"${app.appliedDate.replace(/"/g, '""')}"`,
            `"${app.status.replace(/"/g, '""')}"`,
            `"${app.nextAction.replace(/"/g, '""')}"`,
            `"${app.notes.replace(/"/g, '""')}"`,
            app.url ? `"=HYPERLINK(""${app.url}"",""View Job Posting"")"` : '',
          ].join(',')
        ),
      ].join('\n');

      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.setAttribute('href', url);
      link.setAttribute('download', 'job_applications.csv');
      link.style.visibility = 'hidden';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      // Show success toast
      toast.success('CSV file exported successfully!');
    } catch (error) {
      toast.error('Failed to export CSV file');
      console.error('Export error:', error);
    } finally {
      // Small delay to prevent immediate re-clicks
      setTimeout(() => {
        isExporting = false;
      }, 500);
    }
  }

  // List view now uses DataTable component instead of ListView

  function handleKanbanFinalize(event: CustomEvent) {
    const { columnId, items } = event.detail;

    // Find the items that were moved to this column
    const movedItems = items.filter((item: any) => !item.columnId || item.columnId !== columnId);

    // Update the status of moved items in the applications array
    if (movedItems.length > 0) {
      applications = applications.map((app) => {
        const movedItem = movedItems.find((item: any) => item.id === app.id);
        if (movedItem) {
          // Update the status and show a toast notification
          toast.success(`Moved "${app.position}" to ${columnId}`);
          return { ...app, status: columnId };
        }
        return app;
      });

      // Update the grouped applications for all columns
      groupedApplications = columns.reduce((acc, column) => {
        acc[column.id] = applications.filter((app) => app.status === column.id);
        return acc;
      }, {});
    }
  }
</script>

<SEO
  title="Job Tracker | Hirli"
  description="Track your job applications in one place. Organize, monitor, and optimize your entire job search process with our intuitive job tracker."
  keywords="job tracker, job applications, job search, application tracking, job status management, application organization" />

<!-- Tabs for different sections -->
<Tabs.Root
  value={activeView ? 'active' : 'archived'}
  onValueChange={(value) => (activeView = value === 'active')}>
  <div class="p-0">
    <Tabs.List class="border-t-0">
      <Tabs.Trigger value="active" class="flex-1 gap-2">
        <Briefcase class="h-4 w-4" />
        <span>Active Applications</span>
      </Tabs.Trigger>
      <Tabs.Trigger value="archived" class="flex-1 gap-2">
        <CheckCircle class="h-4 w-4" />
        <span>Archived</span>
      </Tabs.Trigger>
    </Tabs.List>
  </div>

  <!-- Active Applications Tab -->
  <Tabs.Content value="active" class="p-4">
    <!-- Header with actions -->
    <div class="mb-6 flex items-center justify-between">
      <!-- Results and View Toggle -->
      <div class="flex items-center gap-4">
        <Badge variant="secondary" class="text-sm">
          {filteredApplications.length} applications
        </Badge>

        <!-- View Mode Toggle -->
        <Tabs.Root value={viewMode} onValueChange={(value) => (viewMode = value)}>
          <Tabs.List class="h-8">
            <Tabs.Trigger value="kanban" class="h-6 px-3">
              <LayoutGrid class="h-3 w-3" />
            </Tabs.Trigger>
            <Tabs.Trigger value="list" class="h-6 px-3">
              <List class="h-3 w-3" />
            </Tabs.Trigger>
          </Tabs.List>
        </Tabs.Root>
      </div>

      <!-- Action Buttons -->
      <div class="flex items-center gap-2">
        <div class="relative">
          <Search class="text-muted-foreground absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2" />
          <Input
            type="text"
            placeholder="Search applications..."
            class="w-64 pl-9"
            bind:value={searchQuery} />
        </div>
        <Button
          variant="outline"
          onclick={() => exportCSV()}
          disabled={isExporting}
          class="flex items-center gap-2">
          <Download class={`h-4 w-4 ${isExporting ? 'animate-pulse' : ''}`} />
          {isExporting ? 'Exporting...' : 'Export'}
        </Button>
        <Button onclick={() => (newJobModalOpen = true)} class="flex items-center gap-2">
          <Plus class="h-4 w-4" />
          Add Application
        </Button>
      </div>
    </div>
    {#if applications.length === 0}
      <div class="rounded-lg border p-6">
        <div class="text-center">
          <Briefcase class="text-muted-foreground mx-auto h-12 w-12" />
          <h3 class="mt-4 text-lg font-medium">No applications yet</h3>
          <p class="text-muted-foreground mt-2">
            Start tracking your job applications by adding them manually or applying to jobs through
            our platform.
          </p>
          <Button onclick={() => (newJobModalOpen = true)} class="mt-4">
            <Plus class="mr-2 h-4 w-4" />
            Add Your First Application
          </Button>
        </div>
      </div>
    {:else if filteredApplications.length === 0}
      <div class="rounded-lg border p-6">
        <div class="text-center">
          <Search class="text-muted-foreground mx-auto h-12 w-12" />
          <h3 class="mt-4 text-lg font-medium">No applications found</h3>
          <p class="text-muted-foreground mt-2">
            No applications match your search criteria. Try adjusting your search terms.
          </p>
          <Button variant="outline" onclick={() => (searchQuery = '')} class="mt-4">
            Clear Search
          </Button>
        </div>
      </div>
    {:else if viewMode === 'kanban'}
      <!-- Kanban Board -->
      <KanbanView
        {columns}
        {groupedApplications}
        {openApplicationDetails}
        on:finalize={handleKanbanFinalize} />
    {:else}
      <!-- List View with Data Table -->
      <div class="space-y-4">
        <DataTable data={filteredApplications} {openApplicationDetails} bind:searchTerm />
      </div>
    {/if}
  </Tabs.Content>

  <!-- Archived Applications Tab -->
  <Tabs.Content value="archived" class="p-4">
    <!-- Header with actions -->
    <div class="mb-6 flex items-center justify-between">
      <!-- Results -->
      <div class="flex items-center gap-4">
        <Badge variant="secondary" class="text-sm">
          {applications.filter((app) => app.status === 'Rejected' || app.status === 'Offer').length}
          archived
        </Badge>
      </div>

      <!-- Search -->
      <div class="relative">
        <Search class="text-muted-foreground absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2" />
        <Input
          type="text"
          placeholder="Search archived applications..."
          class="w-64 pl-9"
          bind:value={searchQuery} />
      </div>
    </div>

    {#if applications.filter((app) => app.status === 'Rejected' || app.status === 'Offer').length === 0}
      <div class="rounded-lg border p-6">
        <div class="text-center">
          <CheckCircle class="text-muted-foreground mx-auto h-12 w-12" />
          <h3 class="mt-4 text-lg font-medium">No archived applications</h3>
          <p class="text-muted-foreground mt-2">
            Applications that are rejected or result in offers will appear here.
          </p>
        </div>
      </div>
    {:else}
      <!-- Archived Applications List -->
      <div class="space-y-4">
        <DataTable data={filteredApplications} {openApplicationDetails} bind:searchTerm />
      </div>
    {/if}
  </Tabs.Content>
</Tabs.Root>

<!-- Application Details Sheet -->
<ApplicationDetailsSheet bind:sheetOpen {selectedApplication} {statusColors} />

<!-- New Job Modal -->
<AddJobModal
  open={newJobModalOpen}
  on:openChange={(e) => (newJobModalOpen = e.detail)}
  {form}
  {errors}
  {constraints}
  {submitting}
  {enhance}
  {reset}
  {companies}
  {jobTypes}
  {jobStatuses}
  {resumeOptions} />
