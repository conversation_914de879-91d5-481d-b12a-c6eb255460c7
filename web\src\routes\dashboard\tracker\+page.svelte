<script lang="ts">
  import { superForm } from 'sveltekit-superforms/client';
  import { zodClient } from 'sveltekit-superforms/adapters';
  import { toast } from 'svelte-sonner';
  import { jobApplicationSchema } from '$lib/validators/jobApplication';
  import { List, LayoutGrid, Download, Plus, Search } from 'lucide-svelte';
  import { Button } from '$lib/components/ui/button';
  import * as Tabs from '$lib/components/ui/tabs';
  import SEO from '$components/shared/SEO.svelte';
  import AddJobModal from './components/AddJobModal.svelte';
  import KanbanView from './components/KanbanView.svelte';
  import ApplicationDetailsSheet from './components/ApplicationDetailsSheet.svelte';
  import { Input } from '$lib/components/ui/input';
  import { browser } from '$app/environment';
  import DataTable from './data-table-new.svelte';

  import { statusColors } from './types';

  export let data;

  // Set up the form with validation
  const { form, enhance, reset, errors, constraints, submitting } = superForm(data.form, {
    validators: zodClient(jobApplicationSchema),
    dataType: 'json',
    resetForm: true,
    onResult: ({ result }) => {
      if (result.type === 'success') {
        // Add the new application to the list
        const newApplication = {
          id: result.data?.application?.id || crypto.randomUUID(),
          company: $form.company || '',
          position: $form.position || '',
          location: $form.location || 'Remote',
          appliedDate: $form.appliedDate || '',
          status: $form.status || 'Applied',
          nextAction: $form.nextAction || '',
          notes: $form.notes || '',
          logo: 'https://placehold.co/100x100',
          url: $form.url || '',
          jobType: $form.jobType || 'Full-time',
          resumeUploaded: $form.resumeUploaded || 'No',
        };

        // Update applications array with the new job
        applications = [...applications, newApplication];

        // Close modal and show success message
        newJobModalOpen = false;
        toast.success('New job application added successfully!');
      } else if (result.type === 'failure') {
        toast.error('Failed to add job application. Please try again.');
      }
    },
  });

  console.log(data);

  // Add state for the sheet
  let sheetOpen = false;
  let selectedApplication = null;

  // Add state for the new job modal
  let newJobModalOpen = false;

  // Company options
  const companies = [
    'TechCorp',
    'DataSystems Inc',
    'InnovateTech',
    'CloudSolutions',
    'FinTech Global',
    'Other',
  ];

  // Job type options
  const jobTypes = ['Full-time', 'Part-time', 'Contract', 'Freelance', 'Internship'];

  // Job status options
  const jobStatuses = ['Applied', 'Interview', 'Assessment', 'Offer', 'Rejected'];

  // Resume uploaded options
  const resumeOptions = ['Yes', 'No', 'N/A'];

  // Function to open the sheet with the selected application
  function openApplicationDetails(application: any) {
    selectedApplication = application;
    sheetOpen = true;
  }

  // Use the applications from the server
  let applications: any[] = data.applications || [];

  let activeView = true; // true for active, false for archived
  let searchQuery = '';
  let viewMode = 'kanban'; // 'kanban' or 'list'

  // Status colors and icons are now imported from types.ts

  // Define the search term for filtering
  let searchTerm = '';

  // Define the columns for our Kanban board
  const columns = [
    { id: 'Applied', name: 'Applied' },
    { id: 'Interview', name: 'Interview' },
    { id: 'Assessment', name: 'Assessment' },
    { id: 'Offer', name: 'Offer' },
    { id: 'Rejected', name: 'Rejected' },
  ];

  // Filter applications based on active/archived view and search query
  $: filteredApplications = applications.filter((app) => {
    const isArchived = app.status === 'Rejected' || app.status === 'Offer';
    const matchesView = activeView ? !isArchived : isArchived;
    const matchesSearch =
      searchQuery === '' ||
      app.position.toLowerCase().includes(searchQuery.toLowerCase()) ||
      app.company.toLowerCase().includes(searchQuery.toLowerCase());
    return matchesView && matchesSearch;
  });

  // Group applications by status for Kanban view
  $: groupedApplications = columns.reduce((acc, column) => {
    acc[column.id] = filteredApplications.filter((app) => app.status === column.id);
    return acc;
  }, {});

  // Add some CSS for drag and drop visual feedback, but only in the browser
  $: if (browser) {
    // This will only run in the browser, not during SSR
    const dragStyle = document.createElement('style');
    dragStyle.textContent = `
      .dragging {
        opacity: 0.7;
        transform: scale(0.95);
        box-shadow: 0 10px 15px rgba(0, 0, 0, 0.1);
      }
    `;
    document.head.appendChild(dragStyle);
  }

  // Add state to track export in progress
  let isExporting = false;

  // Export applications as CSV
  async function exportCSV() {
    if (isExporting) return; // Prevent double clicks

    isExporting = true;

    try {
      const headers = [
        'Company',
        'Position',
        'Location',
        'Applied Date',
        'Status',
        'Next Action',
        'Notes',
        'URL',
      ];
      const csvContent = [
        headers.join(','),
        ...applications.map((app) =>
          [
            `"${app.company.replace(/"/g, '""')}"`,
            `"${app.position.replace(/"/g, '""')}"`,
            `"${app.location.replace(/"/g, '""')}"`,
            `"${app.appliedDate.replace(/"/g, '""')}"`,
            `"${app.status.replace(/"/g, '""')}"`,
            `"${app.nextAction.replace(/"/g, '""')}"`,
            `"${app.notes.replace(/"/g, '""')}"`,
            app.url ? `"=HYPERLINK(""${app.url}"",""View Job Posting"")"` : '',
          ].join(',')
        ),
      ].join('\n');

      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.setAttribute('href', url);
      link.setAttribute('download', 'job_applications.csv');
      link.style.visibility = 'hidden';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      // Show success toast
      toast.success('CSV file exported successfully!');
    } catch (error) {
      toast.error('Failed to export CSV file');
      console.error('Export error:', error);
    } finally {
      // Small delay to prevent immediate re-clicks
      setTimeout(() => {
        isExporting = false;
      }, 500);
    }
  }

  // List view now uses DataTable component instead of ListView

  function handleKanbanFinalize(event: CustomEvent) {
    const { columnId, items } = event.detail;

    // Find the items that were moved to this column
    const movedItems = items.filter((item: any) => !item.columnId || item.columnId !== columnId);

    // Update the status of moved items in the applications array
    if (movedItems.length > 0) {
      applications = applications.map((app) => {
        const movedItem = movedItems.find((item: any) => item.id === app.id);
        if (movedItem) {
          // Update the status and show a toast notification
          toast.success(`Moved "${app.position}" to ${columnId}`);
          return { ...app, status: columnId };
        }
        return app;
      });

      // Update the grouped applications for all columns
      groupedApplications = columns.reduce((acc, column) => {
        acc[column.id] = applications.filter((app) => app.status === column.id);
        return acc;
      }, {});
    }
  }
</script>

<SEO
  title="Job Tracker | Hirli"
  description="Track your job applications in one place. Organize, monitor, and optimize your entire job search process with our intuitive job tracker."
  keywords="job tracker, job applications, job search, application tracking, job status management, application organization" />

<div class="flex flex-col">
  <!-- Header -->
  <div class="flex flex-col">
    <div
      class="border-border flex flex-col gap-4 border border-b border-l border-r border-t pl-6 sm:flex-row sm:items-center sm:justify-between">
      <div class="flex items-center gap-8">
        <div>{applications.length} Total Jobs</div>
        <Tabs.Root value="active" class="w-full sm:w-auto">
          <Tabs.List class="h-auto p-0">
            <Tabs.Trigger value="active">Active</Tabs.Trigger>
            <Tabs.Trigger value="archived">Archived</Tabs.Trigger>
          </Tabs.List>
        </Tabs.Root>
      </div>
      <div class="flex flex-row text-sm">
        <Button
          class="border-border hover:bg-muted flex cursor-pointer flex-row gap-2 rounded-none border border-b border-r border-t p-6 font-light transition-colors disabled:cursor-not-allowed disabled:opacity-50"
          onclick={() => exportCSV()}
          disabled={isExporting}>
          <Download class={`h-4 w-4 ${isExporting ? 'animate-pulse' : ''}`} />
          {isExporting ? 'Exporting...' : 'Export CSV'}
        </Button>
        <Button
          class="border-border hover:bg-muted flex cursor-pointer flex-row gap-2 rounded-none border border-b border-r border-t p-6 font-light transition-colors disabled:cursor-not-allowed disabled:opacity-50"
          onclick={() => (newJobModalOpen = true)}>
          <Plus class="h-4 w-4" />
          Add Application
        </Button>
      </div>
    </div>
    <div class="flex flex-row gap-4 sm:flex-row sm:items-center sm:justify-between">
      <div class="relative w-full sm:w-80">
        <Search class="text-muted-foreground absolute right-3 top-1/2 h-4 w-4 -translate-y-1/2" />
        <Input
          type="text"
          placeholder="Search applications..."
          class="border-border rounded-none border-b-0 border-l-0 border-r border-t-0 p-6 pr-10"
          bind:value={searchQuery} />
      </div>
      <div class="flex flex-wrap gap-3 text-sm">
        <Tabs.Root value={viewMode} class="w-full sm:w-auto">
          <Tabs.List class="p-0">
            <Tabs.Trigger
              class={`p-4 ${viewMode === 'kanban' ? 'bg-primary text-primary-foreground' : 'bg-transparent'}`}
              value="kanban">
              <LayoutGrid class="h-4 w-4" />
            </Tabs.Trigger>
            <Tabs.Trigger
              class={`p-4 ${viewMode === 'list' ? 'bg-primary text-primary-foreground' : 'bg-transparent'}`}
              value="list">
              <List class="h-4 w-4" />
            </Tabs.Trigger>
          </Tabs.List>
        </Tabs.Root>
      </div>
    </div>
  </div>
  {#if viewMode === 'kanban'}
    <!-- Kanban Board -->
    <KanbanView
      {columns}
      {groupedApplications}
      {openApplicationDetails}
      on:finalize={handleKanbanFinalize} />
  {:else}
    <!-- List View with Data Table -->
    <div class="space-y-4">
      <DataTable data={filteredApplications} {openApplicationDetails} bind:searchTerm />
    </div>
  {/if}
</div>

<!-- Application Details Sheet -->
<ApplicationDetailsSheet bind:sheetOpen {selectedApplication} {statusColors} />

<!-- New Job Modal -->
<AddJobModal
  open={newJobModalOpen}
  on:openChange={(e) => (newJobModalOpen = e.detail)}
  {form}
  {errors}
  {constraints}
  {submitting}
  {enhance}
  {reset}
  {companies}
  {jobTypes}
  {jobStatuses}
  {resumeOptions} />
