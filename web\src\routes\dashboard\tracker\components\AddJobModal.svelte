<script lang="ts">
  import * as Dialog from '$lib/components/ui/dialog';
  import { Button } from '$lib/components/ui/button';
  import { Label } from '$lib/components/ui/label';
  import { Input } from '$lib/components/ui/input';
  import { Textarea } from '$lib/components/ui/textarea';
  import * as Select from '$lib/components/ui/select';
  import { Loader2, Plus } from 'lucide-svelte';

  // Props
  let {
    open = $bindable(),
    form,
    enhance,
    reset,
    errors,
    constraints,
    submitting,
    jobTypes,
    jobStatuses,
    resumeOptions,
  } = $props();

  // Reset form when modal is closed
  $effect(() => {
    if (!open) {
      reset();
    }
  });
</script>

<Dialog.Root bind:open>
  <Dialog.Content class="sm:max-w-[600px]">
    <Dialog.Header>
      <Dialog.Title>Add New Job Application</Dialog.Title>
      <Dialog.Description>Enter the details of your new job application.</Dialog.Description>
    </Dialog.Header>

    <form method="POST" action="?/addJob" use:enhance>
      <div class="grid grid-cols-1 gap-4 py-4 md:grid-cols-2">
        <!-- Company -->
        <div class="space-y-2">
          <Label for="company">Company *</Label>
          <Input
            id="company"
            name="company"
            bind:value={$form.company}
            placeholder="Enter company name"
            {...$constraints.company} />
          {#if $errors.company}
            <p class="text-sm text-red-500">{$errors.company}</p>
          {/if}
        </div>

        <!-- Position -->
        <div class="space-y-2">
          <Label for="position">Position *</Label>
          <Input
            id="position"
            name="position"
            bind:value={$form.position}
            placeholder="e.g. Software Engineer"
            {...$constraints.position} />
          {#if $errors.position}
            <p class="text-sm text-red-500">{$errors.position}</p>
          {/if}
        </div>

        <!-- Location -->
        <div class="space-y-2">
          <Label for="location">Location</Label>
          <Input
            id="location"
            name="location"
            bind:value={$form.location}
            placeholder="San Francisco, CA"
            {...$constraints.location} />
          {#if $errors.location}
            <p class="text-sm text-red-500">{$errors.location}</p>
          {/if}
        </div>

        <!-- Job Type -->
        <div class="space-y-2">
          <Label for="jobType">Job Type *</Label>
          <Select.Root
            type="single"
            value={$form.jobType}
            onValueChange={(value) => {
              $form.jobType = value || '';
            }}>
            <Select.Trigger id="jobType" class="w-full">
              <Select.Value placeholder="Select job type" />
            </Select.Trigger>
            <Select.Content>
              <Select.Group>
                {#each jobTypes as type}
                  <Select.Item value={type}>{type}</Select.Item>
                {/each}
              </Select.Group>
            </Select.Content>
          </Select.Root>
          <input type="hidden" name="jobType" bind:value={$form.jobType} />
          {#if $errors.jobType}
            <p class="text-sm text-red-500">{$errors.jobType}</p>
          {/if}
        </div>

        <!-- Applied Date -->
        <div class="space-y-2">
          <Label for="appliedDate">Applied Date *</Label>
          <Input
            id="appliedDate"
            name="appliedDate"
            type="date"
            bind:value={$form.appliedDate}
            {...$constraints.appliedDate} />
          {#if $errors.appliedDate}
            <p class="text-sm text-red-500">{$errors.appliedDate}</p>
          {/if}
        </div>

        <!-- Status -->
        <div class="space-y-2">
          <Label for="status">Status *</Label>
          <Select.Root
            type="single"
            value={$form.status}
            onValueChange={(value) => {
              $form.status = value || '';
            }}>
            <Select.Trigger id="status" class="w-full">
              <Select.Value placeholder="Select status" />
            </Select.Trigger>
            <Select.Content>
              <Select.Group>
                {#each jobStatuses as status}
                  <Select.Item value={status}>{status}</Select.Item>
                {/each}
              </Select.Group>
            </Select.Content>
          </Select.Root>
          <input type="hidden" name="status" bind:value={$form.status} />
          {#if $errors.status}
            <p class="text-sm text-red-500">{$errors.status}</p>
          {/if}
        </div>

        <!-- Resume -->
        <div class="space-y-2">
          <Label for="resumeUploaded">Resume *</Label>
          <Select.Root
            type="single"
            value={$form.resumeUploaded}
            onValueChange={(value) => {
              $form.resumeUploaded = value || '';
            }}>
            <Select.Trigger id="resumeUploaded" class="w-full">
              <Select.Value placeholder="Select resume status" />
            </Select.Trigger>
            <Select.Content>
              <Select.Group>
                {#each resumeOptions as option}
                  <Select.Item value={option}>{option}</Select.Item>
                {/each}
              </Select.Group>
            </Select.Content>
          </Select.Root>
          <input type="hidden" name="resumeUploaded" bind:value={$form.resumeUploaded} />
          {#if $errors.resumeUploaded}
            <p class="text-sm text-red-500">{$errors.resumeUploaded}</p>
          {/if}
        </div>

        <!-- URL -->
        <div class="space-y-2">
          <Label for="url">Job URL</Label>
          <Input
            id="url"
            name="url"
            bind:value={$form.url}
            placeholder="https://example.com/job/123"
            {...$constraints.url} />
          {#if $errors.url}
            <p class="text-sm text-red-500">{$errors.url}</p>
          {/if}
        </div>

        <!-- Next Action -->
        <div class="space-y-2">
          <Label for="nextAction">Next Action</Label>
          <Input
            id="nextAction"
            name="nextAction"
            bind:value={$form.nextAction}
            placeholder="Follow up with recruiter"
            {...$constraints.nextAction} />
          {#if $errors.nextAction}
            <p class="text-sm text-red-500">{$errors.nextAction}</p>
          {/if}
        </div>

        <!-- Notes - Full width -->
        <div class="space-y-2 md:col-span-2">
          <Label for="notes">Notes</Label>
          <Textarea
            id="notes"
            name="notes"
            bind:value={$form.notes}
            placeholder="Any additional notes about this application"
            {...$constraints.notes} />
          {#if $errors.notes}
            <p class="text-sm text-red-500">{$errors.notes}</p>
          {/if}
        </div>
      </div>

      <div class="mt-4 flex justify-end space-x-2">
        <Button
          type="button"
          variant="outline"
          onclick={() => {
            reset();
            open = false;
          }}>Cancel</Button>
        <Button
          type="submit"
          disabled={submitting}
          class="bg-primary text-primary-foreground hover:bg-primary/90">
          {#if submitting}
            <Loader2 class="mr-2 h-4 w-4 animate-spin" />
            Saving...
          {:else}
            <Plus class="mr-2 h-4 w-4" />
            Add Application
          {/if}
        </Button>
      </div>
    </form>
  </Dialog.Content>
</Dialog.Root>
